import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { MongooseModule } from '@nestjs/mongoose';
import { TelegramController } from './telegram.controller';
import { TelegramToGameHandle } from './handler/telegram-to-game.handle';
import { SyncConfig, SyncConfigSchema } from '../../schemas/sync-config.schema';
import { queues } from '../../constant/mq.constant';

@Module({
  imports: [
    BullModule.registerQueue({
      name: queues.MESSAGE_SYNC_QUEUE,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 50,
      },
    }),
    MongooseModule.forFeature([{ name: SyncConfig.name, schema: SyncConfigSchema }]),
  ],
  controllers: [TelegramController],
  providers: [TelegramToGameHandle],
})
export class TelegramModule {}
