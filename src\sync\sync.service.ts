import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { InjectQueue } from '@nestjs/bull';
import { Model } from 'mongoose';
import { Queue } from 'bull';
import { Telegraf } from 'telegraf';
import { SyncDto } from '../dto/sync.dto';
import { SyncConfig } from '../schemas/sync-config.schema';
import { queues, jobs } from '../constant/mq.constant';

@Injectable()
export class SyncService implements OnModuleInit {
  private bot: Telegraf;
  private isListening = false;
  private logger = Logger;

  constructor(
    @InjectModel(SyncConfig.name) private readonly syncConfigModel: Model<SyncConfig>,
    @InjectQueue(queues.MESSAGE_SYNC_QUEUE) private readonly syncQueue: Queue,
  ) {
    this.bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN || '');
    this.setupBot();
  }

  async onModuleInit() {
    await this.startBotListening();
  }

  async syncTelegramToGame(dto: SyncDto) {
    this.logger.log(`🔍 检查群组配置: telegramGroupId=${dto.chatId}`);
    const config = await this.syncConfigModel.findOne({ telegramGroupId: dto.chatId, enabled: true });
    if (!config) {
      this.logger.warn(`⚠️ 群组未配置或未启用: telegramGroupId=${dto.chatId}`);
      return { success: false, error: '群组未配置' };
    }

    const message = {
      messageId: dto.messageId,
      fromUser: dto.fromUser,
      content: dto.content,
      replyTo: dto.replyTo,
      isAdmin: dto.isAdmin || false
    };

    this.logger.log(`📝 将消息加入队列: ${jobs.TELEGRAM_TO_GAME}, messageId=${dto.messageId}`);
    await this.syncQueue.add(jobs.TELEGRAM_TO_GAME, { message, config });
    return { success: true };
  }

  async syncGameToTelegram(dto: SyncDto) {
    this.logger.log(`🔍 检查频道配置: gameChatChannelId=${dto.chatId}`);
    const config = await this.syncConfigModel.findOne({ gameChatChannelId: dto.chatId, enabled: true });
    if (!config) {
      this.logger.warn(`⚠️ 频道未配置或未启用: gameChatChannelId=${dto.chatId}`);
      return { success: false, error: '频道未配置' };
    }

    const message = {
      messageId: dto.messageId,
      fromUser: dto.fromUser,
      content: dto.content,
      replyTo: dto.replyTo
    };

    this.logger.log(`📝 将消息加入队列: ${jobs.GAME_TO_TELEGRAM}, messageId=${dto.messageId}`);
    await this.syncQueue.add(jobs.GAME_TO_TELEGRAM, { message, config });
    return { success: true };
  }

  async getConfigs() {
    const configs = await this.syncConfigModel.find({ enabled: true });
    return { success: true, data: configs };
  }

  async updateConfig(config: any) {
    await this.syncConfigModel.findOneAndUpdate(
      { telegramGroupId: config.telegramGroupId },
      config,
      { upsert: true, new: true }
    );
    return { success: true };
  }

  private setupBot() {
    this.bot.on('message', async (ctx) => {
      const message = ctx.message;
      const chatId = message.chat.id.toString();
      
      this.logger.log(`📱 收到Telegram消息: chatId=${chatId}, messageId=${message.message_id}`);
      
      const config = await this.syncConfigModel.findOne({ telegramGroupId: chatId, enabled: true });
      if (!config) {
        this.logger.warn(`⚠️ 未找到群组配置或群组未启用: chatId=${chatId}`);
        return;
      }

      // 只处理文本消息
      if ('text' in message) {
        const syncDto: SyncDto = {
          messageId: message.message_id.toString(),
          fromUser: message.from?.username || message.from?.first_name || 'Unknown',
          content: message.text || '',
          timestamp: message.date * 1000,
          chatId: chatId,
          threadId: (message as any).message_thread_id?.toString(),
          replyTo: (message as any).reply_to_message?.message_id?.toString(),
          
          isAdmin: false,
        };

        this.logger.log(`📤 准备同步消息到游戏: chatId=${chatId}, user=${syncDto.fromUser}, content="${syncDto.content.substring(0, 50)}${syncDto.content.length > 50 ? '...' : ''}"`);
        
        try {
          const result = await this.syncTelegramToGame(syncDto);
          if (result.success) {
            this.logger.log(`✅ 消息同步成功: messageId=${syncDto.messageId}`);
          } else {
            this.logger.error(`❌ 消息同步失败: ${result.error}`);
          }
        } catch (error) {
          this.logger.error(`❌ 消息同步异常:`, error);
        }
      } else {
        const messageType = this.getMessageType(message);
        this.logger.log(`ℹ️ 跳过非文本消息: messageId=${message.message_id}, type=${messageType}`);
      }
    });
  }

  private async startBotListening() {
    try {
      await this.bot.launch();
      this.isListening = true;
      this.logger.log('🤖 Telegram Bot 启动成功，开始监听消息...');
    } catch (error) {
      this.logger.error('❌ Telegram Bot 启动失败:', error);
    }
  }

    // 添加辅助方法来确定消息类型
  private getMessageType(message: any): string {
    if ('text' in message) return 'text';
    if ('photo' in message) return 'photo';
    if ('video' in message) return 'video';
    if ('audio' in message) return 'audio';
    if ('voice' in message) return 'voice';
    if ('document' in message) return 'document';
    if ('sticker' in message) return 'sticker';
    if ('animation' in message) return 'animation';
    if ('poll' in message) return 'poll';
    if ('location' in message) return 'location';
    return 'unknown';
  }
}
