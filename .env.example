# Telegram 配置
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
# 使用群组用户名，从 https://t.me/grassPush/1 链接中提取
TELEGRAM_GROUP_ID=@grassPush
TELEGRAM_THREAD_ID=1

# 游戏聊天 API 配置
GAME_CHAT_API_URL=http://localhost:8080
# 1001 - 主聊天窗口, 1002 - 系统消息窗口, 1 - 默认窗口
GAME_CHAT_CHANNEL_ID=1001

# Redis配置(集群版本)
#REDIS_TYPE=cluster
#REDIS_NODES=
#REDIS_PORT=
#REDIS_PASSWORD=
#REDIS_DATABASE=

# Redis配置(主备版本)
REDIS_TYPE=single
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DATABASE=1

# Redis MQ配置
MQ_REDIS_URL=redis://localhost:6379
MQ_REDIS_PASSWORD=
MQ_REDIS_DATABASE=2

# 服务配置
PORT=3000
NODE_ENV=development

# Telegram Bot 配置
FORCE_REAL_MODE=true
DISABLE_TEST_MODE=true

# 管理员配置（逗号分隔的用户名列表，支持带@或不带@的格式）
TELEGRAM_ADMIN_USERNAMES=@admin1,admin2,@admin3

# 多群组配置（可选）
# 启用多群组模式时，可以配置多个Telegram群组对应不同的游戏聊天频道
# 格式：JSON数组，每个对象包含telegramGroupId、gameChatChannelId和可选的telegramThreadId
# MULTI_GROUP_MAPPINGS=[{"telegramGroupId":"@group1","gameChatChannelId":"1001","telegramThreadId":"1"},{"telegramGroupId":"@group2","gameChatChannelId":"1002","telegramThreadId":"2"}]

# 代理配置（可选）
HTTP_PROXY=http://127.0.0.1:7897
HTTPS_PROXY=http://127.0.0.1:7897
SOCKS_PROXY=socks5://127.0.0.1:7897

BOT_INSTANCE_ID=unique_instance_id
MAX_BOT_RETRIES=3
BOT_RESTART_DELAY=10000