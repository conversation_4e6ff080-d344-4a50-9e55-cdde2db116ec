/**
 * @auther bc1pAzerty
 * @date 2024/10/12 12:21
 */
export const httpCode = {
  // 通用错误码范围 0 - 100
  UNISAT_OK: {
    code: 0,
    msg: 'ok',
  },

  OK: {
    code: 1,
    msg: 'ok',
  },

  FAILED: {
    code: 2,
    msg: 'failed',
  },

  UNKNOWN_ERROR: {
    code: 3,
    msg: 'unknown error',
  },

  // Telegram相关错误码 100-199
  TELEGRAM_API_ERROR: {
    code: 100,
    msg: 'Telegram API调用失败',
  },

  TELEGRAM_RATE_LIMIT: {
    code: 101,
    msg: 'Telegram API频率限制',
  },

  TELEGRAM_PERMISSION_ERROR: {
    code: 102,
    msg: 'Telegram Bot权限不足',
  },

  TELEGRAM_NETWORK_ERROR: {
    code: 103,
    msg: 'Telegram网络连接错误',
  },

  // 消息处理错误码 200-299
  MESSAGE_VALIDATION_ERROR: {
    code: 200,
    msg: '消息格式验证失败',
  },

  MESSAGE_QUEUE_ERROR: {
    code: 201,
    msg: '消息队列处理失败',
  },

  // 配置错误码 300-399
  CONFIG_ERROR: {
    code: 300,
    msg: '配置错误',
  },
};

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  RATE_LIMIT = 'RATE_LIMIT',
  CONFIG = 'CONFIG',
  UNKNOWN = 'UNKNOWN',
}

// 错误严重级别
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}
