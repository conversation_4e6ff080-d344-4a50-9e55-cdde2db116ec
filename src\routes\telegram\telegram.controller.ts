import { Controller, Post, Body, Get, Logger, HttpException, HttpStatus, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiQuery } from '@nestjs/swagger';
import { SyncDto } from '../../dto/sync.dto';

@ApiTags('Telegram')
@Controller('telegram')
export class TelegramController {
  private readonly logger = new Logger(TelegramController.name);

  /**
   * Telegram消息同步到游戏
   */
  @Post('sync-to-game')
  @ApiOperation({ summary: 'Telegram消息同步到游戏' })
  @ApiBody({ type: SyncDto })
  @ApiResponse({ status: 200, description: '同步成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async syncToGame(@Body() syncDto: SyncDto) {
    try {
      this.logger.log(`接收到Telegram消息同步请求: ${syncDto.messageId}`);
      
      // 验证必要参数
      if (!syncDto.content || !syncDto.fromUser || !syncDto.messageId) {
        throw new HttpException('缺少必要参数', HttpStatus.BAD_REQUEST);
      }

      return {
        success: true,
        data: {
          messageId: syncDto.messageId,
          status: 'queued',
          message: '消息已加入处理队列',
        }
      };
    } catch (error) {
      this.logger.error('Telegram消息同步失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 发送消息到Telegram群组
   */
  @Post('send-message')
  @ApiOperation({ summary: '发送消息到Telegram群组' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        chatId: { type: 'string', description: 'Telegram群组ID' },
        message: { type: 'string', description: '消息内容' },
        threadId: { type: 'string', description: '线程ID（可选）' },
        fromUser: { type: 'string', description: '发送用户（可选）' },
      },
      required: ['chatId', 'message'],
    },
  })
  @ApiResponse({ status: 200, description: '发送成功' })
  async sendMessage(@Body() body: { 
    chatId: string; 
    message: string; 
    threadId?: string; 
    fromUser?: string;
  }) {
    throw new HttpException('功能已移除，请使用同步接口', HttpStatus.NOT_IMPLEMENTED);
  }

  /**
   * 获取群组信息
   */
  @Get('chat-info')
  @ApiOperation({ summary: '获取Telegram群组信息' })
  @ApiQuery({ name: 'chatId', description: 'Telegram群组ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getChatInfo(@Query('chatId') chatId: string) {
    throw new HttpException('功能已移除', HttpStatus.NOT_IMPLEMENTED);
  }

  /**
   * Telegram服务健康检查
   */
  @Get('health')
  @ApiOperation({ summary: 'Telegram服务健康检查' })
  @ApiResponse({ status: 200, description: '服务正常' })
  async healthCheck() {
    return {
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
      }
    };
  }

  /**
   * 发送文件到Telegram
   */
  @Post('send-document')
  @ApiOperation({ summary: '发送文件到Telegram群组' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        chatId: { type: 'string', description: 'Telegram群组ID' },
        document: { type: 'string', description: '文件内容或URL' },
        caption: { type: 'string', description: '文件说明（可选）' },
        threadId: { type: 'string', description: '线程ID（可选）' },
      },
      required: ['chatId', 'document'],
    },
  })
  @ApiResponse({ status: 200, description: '发送成功' })
  async sendDocument(@Body() body: { 
    chatId: string; 
    document: any; 
    caption?: string; 
    threadId?: string;
  }) {
    throw new HttpException('功能已移除，请使用同步接口', HttpStatus.NOT_IMPLEMENTED);
  }
}
