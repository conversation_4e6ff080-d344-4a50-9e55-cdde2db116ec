import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { SyncModule } from './sync/sync.module';
import { TelegramController } from './routes/telegram/telegram.controller';
import { GameController } from './routes/game/game.controller';
import { MongooseModule } from '@nestjs/mongoose';
import * as dotenv from 'dotenv';
dotenv.config();

@Module({
  imports: [
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost:27017/chat-sync'),
    SyncModule,
  ],
  controllers: [AppController, TelegramController, GameController],
  providers: [AppService],
})
export class AppModule {}
