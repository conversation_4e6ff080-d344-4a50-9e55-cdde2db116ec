import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { TelegramController } from './routes/telegram/telegram.controller';
import { GameController } from './routes/game/game.controller';
import { SyncController } from './sync/sync.controller';

import * as dotenv from 'dotenv';
dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 配置Swagger文档
  const config = new DocumentBuilder()
    .setTitle('Telegram-游戏聊天同步系统')
    .setDescription('实现 Telegram 群组主题帖与游戏聊天窗口的双向消息同步')
    .setVersion('1.0')
    .build();

  // 创建文档时包含所有控制器
  const document = SwaggerModule.createDocument(app, config, {
    include: [App<PERSON>od<PERSON>, TelegramController, GameController, SyncController],
  });
  SwaggerModule.setup('api-docs', app, document);

  const port = process.env.PORT || 3002;
  
  await app.listen(port, '0.0.0.0');

  // 在启动程序完成后打印日志
  console.log('🚀 应用程序启动成功！');
  console.log(`🌐 服务地址: http://localhost:${port}`);
  console.log(`📚 API 文档: http://localhost:${port}/api-docs`);
  console.log(`🔧 运行环境: ${process.env.NODE_ENV || 'development'}`);
}

bootstrap().catch(console.error);
