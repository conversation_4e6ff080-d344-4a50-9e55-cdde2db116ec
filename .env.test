# Telegram 配置
TELEGRAM_BOT_TOKEN=**********************************************

# 1001 - 主聊天窗口, 1002 - 系统消息窗口, 1 - 默认窗口
# 为了兼容性保留此配置，但在多群组模式下不会使用
GAME_CHAT_CHANNEL_ID=1001

# 多群组配置（可选）
# 启用多群组模式时，可以配置多个Telegram群组对应不同的游戏聊天频道
# 格式：JSON数组，每个对象包含telegramGroupId、gameChatChannelId和可选的telegramThreadId
MULTI_GROUP_MAPPINGS=[{"telegramGroupId":"@grassPush","gameChatChannelId":1001,"telegramThreadId":"1"},{"telegramGroupId":"@testFractalPush","gameChatChannelId":1002}]


# 游戏聊天 API 配置
GAME_CHAT_API_URL=http://135.181.78.188:8080
# 游戏聊天频道ID，用于指定消息发送到哪个游戏窗口


# 管理员配置（逗号分隔的用户名列表，支持带@或不带@的格式）
TELEGRAM_ADMIN_USERNAMES=@alon0918

# 服务配置
PORT=3001
NODE_ENV=development

# Telegram Bot 配置
FORCE_REAL_MODE=true
DISABLE_TEST_MODE=true

# 代理配置（可选）
HTTP_PROXY=http://127.0.0.1:7897
HTTPS_PROXY=http://127.0.0.1:7897
SOCKS_PROXY=socks5://127.0.0.1:7897

# Redis配置
#REDIS_TYPE=cluster
#REDIS_NODES=
#REDIS_PORT=
#REDIS_PASSWORD=
#REDIS_DATABASE=

REDIS_TYPE=single
REDIS_URL=redis://121.91.175.154:6379
REDIS_PASSWORD=3a6f0e74f57F@b91
REDIS_DATABASE=10

# Redis MQ配置
MQ_REDIS_URL=redis://121.91.175.154:6379
MQ_REDIS_PASSWORD=3a6f0e74f57F@b91
MQ_REDIS_DATABASE=11

BOT_INSTANCE_ID=unique_instance_id
MAX_BOT_RETRIES=3
BOT_RESTART_DELAY=10000