import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsOptional, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 简化的消息同步DTO
 */
export class SyncDto {
  @ApiProperty({ description: '消息ID' })
  @IsString()
  @IsNotEmpty()
  messageId: string;

  @ApiProperty({ description: '发送者昵称' })
  @IsString()
  @IsNotEmpty()
  fromUser: string;

  @ApiProperty({ description: '消息内容' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({ description: '聊天ID' })
  @IsString()
  @IsNotEmpty()
  chatId: string;

  @ApiProperty({ description: '消息时间戳' })
  @IsNumber()
  @Type(() => Number)
  timestamp: number;

  @ApiPropertyOptional({ description: '主题帖ID' })
  @IsOptional()
  @IsString()
  threadId?: string;

  @ApiPropertyOptional({ description: '被回复消息的ID' })
  @IsOptional()
  @IsString()
  replyTo?: string;

  @ApiPropertyOptional({ description: '是否为管理员', default: false })
  @IsOptional()
  @IsBoolean()
  isAdmin?: boolean;
}
