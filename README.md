# Telegram-游戏聊天同步系统

<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

一个基于 NestJS 框架的 Telegram 群组主题帖与游戏聊天窗口双向消息同步系统。

## ✨ 主要特性

- 🔄 **双向消息同步**：Telegram ↔ 游戏聊天频道
- 🏢 **多群组支持**：支持多个Telegram群组映射到不同游戏频道
- 📝 **论坛主题帖支持**：完美支持Telegram论坛群组的主题帖功能
- 🚀 **高性能队列系统**：基于Redis和Bull队列，支持消息重试和错误处理
- 🛡️ **安全可靠**：完整的错误处理、消息去重、管理员权限控制
- 🎭 **智能消息过滤**：自动过滤表情符号、多媒体内容，确保消息兼容性
- 📊 **监控友好**：详细的日志记录、健康检查接口
- 🔧 **易于部署**：Docker支持、环境变量配置、自动化测试

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 配置环境变量

```bash
cp .env.example .env
```

编辑 `.env` 文件配置必要参数：

```env
# Telegram Bot 配置
TELEGRAM_BOT_TOKEN=你的机器人Token

# 单群组模式配置（传统方式）
TELEGRAM_GROUP_ID=@grassPush  # 或使用数字ID：*************
TELEGRAM_THREAD_ID=1
GAME_CHAT_CHANNEL_ID=1001

# 多群组模式配置（可选，配置后将启用多群组模式）
# MULTI_GROUP_MAPPINGS=[{"telegramGroupId":"@group1","gameChatChannelId":"1001","telegramThreadId":"1","enabled":true,"priority":1,"messageFilters":{"allowBot":false,"allowMultimedia":false,"allowEmpty":false,"maxMessageLength":1000},"routingRules":{"gameToTelegram":true,"telegramToGame":true}},{"telegramGroupId":"@group2","gameChatChannelId":"1002","enabled":true,"priority":2}]

# 游戏API配置
GAME_CHAT_API_URL=游戏聊天API地址
GAME_CHAT_API_TOKEN=游戏API授权Token

# Redis配置（用于消息队列）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 服务配置
PORT=3000
```

#### 如何获取群组信息？

**方法1：从 Telegram 链接获取（推荐）**
如果您有群组链接如：`https://t.me/grassPush/1`

- 群组用户名：`grassPush` → 配置为 `@grassPush`
- 主题帖ID：`1` → 直接使用 `1`

**方法2：使用机器人获取群组ID**

1. 将机器人添加到群组
2. 让机器人发送消息
3. 使用 Telegram Bot API 获取更新信息

### 启动服务

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### 验证安装

运行集成测试脚本：

```bash
node test-integration.js
```

访问健康检查接口：

```bash
curl http://localhost:3000/health
```

## 🧪 测试

- 单元测试：`npm run test`
- 端到端测试：`npm run test:e2e`
- 集成测试：`node test-integration.js`（需服务运行中）
- Bot 监听测试：`node test-bot-listener.js`

集成测试将验证：环境配置、API健康、Bot连接、消息同步、历史消息、重复消息处理等。

Bot 监听测试将验证：Bot 状态、自动监听、消息接收、服务重启等。

## 🤖 Bot 监听功能

- 应用启动后自动监听配置的 Telegram 群组
- 轮询频率：每秒检查一次新消息
- 支持主题帖过滤和自动同步到游戏 API
- 管理接口：
  - `GET /telegram-bot/status` - 查看监听状态
  - `POST /telegram-bot/restart` - 重启监听服务

## 🏢 多群组配置详解

系统支持两种配置模式：单群组模式（传统）和多群组模式（增强）。

### 单群组模式

使用传统的环境变量配置：

```env
TELEGRAM_GROUP_ID=@grassPush
TELEGRAM_THREAD_ID=1
GAME_CHAT_CHANNEL_ID=1001
```

### 多群组模式

通过 `MULTI_GROUP_MAPPINGS` 环境变量配置多个群组映射：

```env
MULTI_GROUP_MAPPINGS=[
  {
    "telegramGroupId": "@group1",
    "gameChatChannelId": "1001",
    "telegramThreadId": "1",
    "enabled": true,
    "priority": 1,
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": false,
      "allowEmpty": false,
      "maxMessageLength": 1000
    },
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": true
    }
  },
  {
    "telegramGroupId": "@group2",
    "gameChatChannelId": "1002",
    "enabled": true,
    "priority": 2
  }
]
```

### 配置参数说明

| 参数                      | 类型     | 必填 | 默认值 | 说明                                 |
| ------------------------- | -------- | ---- | ------ | ------------------------------------ |
| `telegramGroupId`         | string   | ✅   | -      | Telegram群组ID（@用户名或数字ID）    |
| `gameChatChannelId`       | number   | ✅   | -      | 游戏聊天频道ID                       |
| `telegramThreadId`        | string   | ❌   | -      | 游戏消息推送到Telegram的目标主题帖ID |
| `telegramListenThreadIds` | string[] | ❌   | -      | Telegram消息监听的主题帖ID列表       |
| `enabled`                 | boolean  | ❌   | true   | 是否启用此映射                       |
| `priority`                | number   | ❌   | 0      | 优先级（数字越小优先级越高）         |
| `messageFilters`          | object   | ❌   | 见下表 | 消息过滤规则                         |
| `routingRules`            | object   | ❌   | 见下表 | 路由规则                             |

### 消息过滤规则 (messageFilters)

| 参数               | 类型    | 默认值 | 说明               |
| ------------------ | ------- | ------ | ------------------ |
| `allowBot`         | boolean | false  | 是否允许Bot消息    |
| `allowMultimedia`  | boolean | false  | 是否允许多媒体消息 |
| `allowEmpty`       | boolean | false  | 是否允许空消息     |
| `maxMessageLength` | number  | 1000   | 最大消息长度       |

### 路由规则 (routingRules)

| 参数             | 类型    | 默认值 | 说明                       |
| ---------------- | ------- | ------ | -------------------------- |
| `gameToTelegram` | boolean | true   | 允许游戏消息推送到Telegram |
| `telegramToGame` | boolean | true   | 允许Telegram消息推送到游戏 |

### 配置示例

**基础多群组配置：**

```json
[
  {
    "telegramGroupId": "@maingroup",
    "gameChatChannelId": 1001
  },
  {
    "telegramGroupId": "-*************",
    "gameChatChannelId": 1002,
    "telegramThreadId": "5"
  }
]
```

**高级配置示例：**

```json
[
  {
    "telegramGroupId": "@vipgroup",
    "gameChatChannelId": 1001,
    "telegramThreadId": "183",
    "telegramListenThreadIds": ["1", "8"],
    "enabled": true,
    "priority": 1,
    "messageFilters": {
      "allowBot": false,
      "allowMultimedia": true,
      "maxMessageLength": 500
    },
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": true
    }
  },
  {
    "telegramGroupId": "@readonly",
    "gameChatChannelId": 1002,
    "priority": 2,
    "routingRules": {
      "gameToTelegram": true,
      "telegramToGame": false
    }
  }
]
```

## 🎭 消息过滤功能

系统内置智能消息过滤功能，确保消息推送的稳定性和兼容性：

### 支持的过滤类型

- **表情符号过滤**：自动移除Unicode表情符号（😊🌞🎉等）
- **多媒体内容检测**：跳过照片、视频、贴纸、文档等多媒体消息
- **纯表情消息过滤**：跳过只包含表情符号的消息
- **HTML标签清理**：移除潜在的恶意HTML标签
- **消息长度限制**：根据配置限制消息最大长度
- **Bot消息过滤**：可配置是否允许Bot发送的消息

### 过滤示例

```
原始消息："你好😊今天天气不错🌞"
过滤后："你好今天天气不错"

原始消息："😊😂🤣😍🥰"
处理结果：消息被跳过（纯表情消息）

长消息：超过maxMessageLength限制的消息将被截断或跳过
```

### 测试过滤功能

```bash
# 构建项目
npm run build

# 运行过滤功能测试
node test/test-emoji-filter.js
```

## 📖 API 文档

启动服务后访问：http://localhost:3000/api-docs

## 🔧 核心接口

### Telegram 相关接口

- `POST /telegram/sync-to-game` - Telegram 消息同步到游戏
- `GET /telegram/thread-messages` - 获取 Telegram 主题帖历史消息
- `GET /telegram/health` - Telegram 服务健康检查

### 游戏相关接口

- `POST /game/sync-to-telegram` - 游戏消息同步到 Telegram

### 管理员接口

- `GET /admin/ad-filter/config` - 获取广告过滤配置
- `PUT /admin/ad-filter/config` - 更新广告过滤配置
- `POST /admin/ad-filter/keywords` - 添加广告关键词
- `DELETE /admin/ad-filter/keywords` - 删除广告关键词
- `GET /admin/ad-filter/keywords` - 获取所有关键词
- `POST /admin/ad-filter/whitelist` - 添加白名单用户
- `DELETE /admin/ad-filter/whitelist` - 删除白名单用户
- `GET /admin/ad-filter/whitelist` - 获取白名单
- `POST /admin/ad-filter/test` - 测试消息过滤
- `GET /admin/ad-filter/stats` - 获取过滤统计
- `POST /admin/ad-filter/stats/reset` - 重置过滤统计

### Bot 管理接口

- `GET /telegram-bot/status` - 获取 Bot 监听状态
- `POST /telegram-bot/restart` - 重启 Bot 监听

### 广告过滤管理接口

- `GET /admin/ad-filter/config` - 获取广告过滤配置
- `PUT /admin/ad-filter/config` - 更新广告过滤配置
- `POST /admin/ad-filter/keywords` - 添加广告关键词
- `DELETE /admin/ad-filter/keywords` - 移除广告关键词
- `POST /admin/ad-filter/whitelist` - 添加白名单关键词
- `POST /admin/ad-filter/test` - 测试消息广告过滤
- `GET /admin/ad-filter/stats` - 获取广告过滤统计
- `POST /admin/ad-filter/reset` - 重置广告过滤配置
- `POST /admin/ad-filter/toggle` - 切换广告过滤开关

### 应用基础接口

- `GET /app/hello` - 基础测试接口

### 请求示例

```bash
# 同步 Telegram 消息到游戏
curl -X POST "http://localhost:3000/telegram/sync-to-game" \
  -H "Content-Type: application/json" \
  -d '{
    "messageId": "12345",
    "username": "用户名",
    "content": "消息内容",
    "timestamp": 1640995200
  }'

# 获取广告过滤配置
curl -X GET "http://localhost:3000/admin/ad-filter/config"

# 更新广告过滤配置
curl -X PUT "http://localhost:3000/admin/ad-filter/config" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "adThreshold": 0.3,
    "enableCapsDetection": true,
    "keywords": ["广告", "推广"],
    "whitelist": ["官方"]
  }'

# 测试消息广告过滤
curl -X POST "http://localhost:3000/admin/ad-filter/test" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "这是一条测试消息"
  }'

# 添加广告关键词
curl -X POST "http://localhost:3000/admin/ad-filter/keywords" \
  -H "Content-Type: application/json" \
  -d '{
    "keywords": ["新广告词", "推销"]
  }'

# 同步游戏消息到 Telegram
curl -X POST "http://localhost:3000/game/sync-to-telegram" \
  -H "Content-Type: application/json" \
  -d '{
    "messageId": "game_12345",
    "fromUser": "玩家名",
    "content": "Hello, Telegram!",
    "timestamp": 1672531200000
  }'
```

## 📂 项目结构

```
src/
├── app.controller.ts          # 应用基础控制器
├── app.module.ts              # 主模块
├── app.service.ts             # 应用基础服务
├── main.ts                    # 应用启动入口
├── config/                    # 配置管理模块
│   ├── config.module.ts
│   └── config.service.ts      # 环境变量和多群组配置
├── dto/                       # 数据传输对象
│   └── sync.dto.ts           # 消息同步DTO
├── message/                   # 消息处理中心
│   ├── message.module.ts
│   └── message.service.ts     # 消息格式化和验证
├── redis/                     # Redis和队列服务
│   ├── redis.module.ts
│   └── redis.service.ts
├── routes/                    # 路由模块
│   ├── admin/                 # 管理员接口
│   │   ├── admin.controller.ts
│   │   ├── admin.module.ts
│   │   └── admin.service.ts
│   ├── game/                  # 游戏相关接口
│   │   ├── game.controller.ts
│   │   ├── game.module.ts
│   │   ├── game.service.ts
│   │   └── handler/           # 消息队列处理器
│   ├── telegram/              # Telegram相关接口
│   │   ├── telegram.controller.ts
│   │   ├── telegram.module.ts
│   │   ├── telegram.service.ts
│   │   └── handler/           # 消息队列处理器
│   └── telegram-bot/          # Bot管理接口
│       ├── telegram-bot.controller.ts
│       ├── telegram-bot.module.ts
│       └── telegram-bot.service.ts
├── telegram/                  # Telegram核心服务
│   └── telegram.service.ts
├── utils/                     # 工具函数
│   ├── basic.utils.ts
│   └── error.utils.ts
└── constant/                  # 常量定义
    ├── http.constant.ts
    ├── magic.constant.ts
    └── mq.constant.ts
test/
├── test-integration.js        # 集成测试脚本
├── test-bot-listener.js       # Bot 监听测试脚本
├── test-game-to-telegram-push.js # 游戏到TG推送测试
└── ...                        # 其他测试脚本
```

## 📋 开发指南

### 📚 完整文档索引

- **[文档索引](./docs/README.md)** - 所有技术文档的完整索引和导航
- **[系统架构完整说明](./docs/系统架构完整说明.md)** - 系统整体架构设计和模块划分
- **[API文档说明](./docs/API文档说明.md)** - 完整的 RESTful API 接口文档
- **[项目开发与交付文档](./docs/项目开发与交付文档.md)** - 开发环境搭建和部署指南

### 🔧 功能特性文档

- **[多群组聊天同步配置说明](./docs/多群组聊天同步配置说明.md)** - 多群组配置和管理
- **[广告过滤功能说明](./docs/广告过滤功能说明.md)** - 智能广告检测和过滤
- **[消息过滤功能说明](./docs/消息过滤功能说明.md)** - 消息过滤规则和配置
- **[消息回复功能实现说明](./docs/消息回复功能实现说明.md)** - 回复链构建和处理
- **[游戏消息推送到Telegram实现说明](./docs/游戏消息推送到Telegram实现说明.md)** - 消息同步机制

## 🛠️ 技术栈

### 🏗️ 核心框架
- **框架**：NestJS 10.x + TypeScript 5.x
- **运行时**：Node.js 18+
- **包管理**：npm

### 📖 API 文档
- **文档生成**：Swagger/OpenAPI 3.0
- **接口测试**：内置 Swagger UI

### ✅ 数据验证
- **参数验证**：class-validator + class-transformer
- **DTO 转换**：自动类型转换和验证

### ⚙️ 配置管理
- **环境变量**：dotenv + 自定义配置服务
- **多群组配置**：JSON 文件 + 动态加载
- **配置验证**：启动时配置完整性检查

### 🔄 消息队列
- **队列系统**：Bull 4.x (基于 Redis)
- **任务调度**：支持延迟、重试和优先级
- **监控面板**：Bull Dashboard (可选)

### 🌐 网络通信
- **HTTP 客户端**：Axios + 连接池
- **Telegram API**：node-telegram-bot-api
- **WebHook 支持**：Express 中间件

### 💾 数据存储
- **缓存**：Redis 7.x
- **配置存储**：JSON 文件
- **日志存储**：文件系统 + 控制台输出

### 🧪 测试框架
- **单元测试**：Jest + Supertest
- **集成测试**：自定义测试脚本
- **覆盖率**：Jest Coverage

### 📏 代码质量
- **代码规范**：ESLint + Prettier
- **类型检查**：TypeScript 严格模式
- **Git 钩子**：Husky + lint-staged

### 🔧 开发工具
- **热重载**：NestJS CLI + nodemon
- **调试支持**：VS Code 调试配置
- **构建工具**：TypeScript 编译器

### 🐳 部署支持
- **容器化**：Docker + Docker Compose
- **进程管理**：PM2 (可选)
- **环境隔离**：多环境配置支持

## 部署说明

- 推荐参考 [NestJS 部署文档](https://docs.nestjs.com/deployment)
- 可选用 [NestJS Mau](https://mau.nestjs.com) 平台一键部署到 AWS

```bash
npm install -g @nestjs/mau
mau deploy
```

## 📚 参考链接

- [NestJS 官方文档](https://docs.nestjs.com)
- [NestJS Mau 云部署](https://mau.nestjs.com)
- [NestJS Devtools](https://devtools.nestjs.com)
- [官方 Discord 社区](https://discord.gg/G7Qnnhy)

## License

MIT License
