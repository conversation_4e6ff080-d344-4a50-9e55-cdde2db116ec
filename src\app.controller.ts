import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('app')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: '应用欢迎信息' })
  @ApiResponse({ status: 200, description: '返回应用信息' })
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ status: 200, description: '服务健康状态' })
  async healthCheck() {
    const health = await this.appService.getHealthStatus();
    return { success: true, data: health };
  }

  @Get('version')
  @ApiOperation({ summary: '获取版本信息' })
  @ApiResponse({ status: 200, description: '返回版本信息' })
  getVersion() {
    const version = this.appService.getVersion();
    return { success: true, data: version };
  }

  @Get('status')
  @ApiOperation({ summary: '获取系统状态' })
  @ApiResponse({ status: 200, description: '返回系统运行状态' })
  async getStatus() {
    const status = await this.appService.getSystemStatus();
    return { success: true, data: status };
  }
}
