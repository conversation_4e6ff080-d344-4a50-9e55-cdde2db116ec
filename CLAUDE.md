# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Telegram-Game Chat Sync Bot** - A NestJS-based bidirectional message synchronization system between Telegram groups and game chat channels.

## Core Architecture

### System Design
- **Framework**: NestJS + TypeScript
- **Purpose**: Synchronize messages between Telegram groups and game chat channels
- **Direction**: Bidirectional (Telegram ↔ Game)
- **Queue System**: Redis + Bull for async message processing
- **Database**: MongoDB for configuration storage

### Key Components

#### 1. Message Flow Architecture
```
Telegram → Bot Listener → Queue → Handler → Game API
Game API → REST Endpoint → Queue → Handler → Telegram Bot
```

#### 2. Core Modules
- **SyncModule**: Central orchestration service
- **AppModule**: Root module with MongoDB connection
- **Queue Processors**: Handle async message processing
- **Telegraf <PERSON>t**: Real-time Telegram message listener

#### 3. Queue System
- **Queue Name**: `message-sync-queue`
- **Job Types**: 
  - `telegram-to-game`: Forward Telegram messages to game
  - `game-to-telegram`: Forward game messages to Telegram

### Essential Commands

#### Development
```bash
# Install dependencies
npm install

# Development with hot reload
npm run start:dev

# Production build
npm run build

# Production start
npm run start:prod

# Health check
curl http://localhost:3002/health
```

#### Testing
```bash
# Unit tests
npm run test

# Integration test (requires running service)
node test-simplified.js
```

#### Code Quality
```bash
# Linting
npm run lint

# Format code
npm run format
```

### Configuration

#### Environment Variables (.env)
```env
PORT=3002
TELEGRAM_BOT_TOKEN=your_bot_token
MONGODB_URI=mongodb://...
GAME_CHAT_API_URL=http://game-api:8080
REDIS_URL=redis://localhost:6379
```

#### Configuration Storage
- **MongoDB Collection**: `sync_configs`
- **Schema**: Unified sync configuration with filtering rules
- **Key Fields**: `telegramGroupId`, `gameChatChannelId`, `enabled`, `messageFilter`

### API Endpoints

#### Core Sync Endpoints
- `POST /sync/telegram-to-game` - Manual Telegram sync
- `POST /sync/game-to-telegram` - Manual game sync
- `GET /sync/configs` - List active configurations
- `POST /sync/config` - Update sync configuration

#### Health & Status
- `GET /health` - Service health check
- `GET /` - Basic hello endpoint

### File Structure Patterns

#### Message Processing
- **Handlers**: `src/routes/*/handler/` - Queue processors
- **Services**: `src/sync/sync.service.ts` - Main sync orchestration
- **Models**: `src/schemas/` - MongoDB schemas

#### Bot Integration
- **Listener**: Automatic in `SyncService` via Telegraf
- **Message Filtering**: Built-in text-only filtering
- **Thread Support**: Via `telegramThreadId` configuration

### Critical Notes

#### Startup Sequence
1. MongoDB connection
2. Redis connection
3. Queue registration
4. Bot initialization and listening
5. Express server startup

#### Message Flow
- **Telegram → Game**: Bot listener → Queue → HTTP POST to game API
- **Game → Telegram**: REST endpoint → Queue → Bot send message
- **Queue Processing**: Async with Bull job system

#### Error Handling
- Queue retries configured in Bull
- MongoDB connection with auto-reconnect
- Bot restart on connection failure

### Development Workflow

1. **Setup**: `npm install` + `.env` configuration
2. **Development**: `npm run start:dev` for hot reload
3. **Testing**: Use provided test scripts for integration
4. **Build**: `npm run build` for production
5. **Deploy**: `npm run start:prod` with PM2

### Key Technologies
- **NestJS**: Main framework
- **Telegraf**: Telegram Bot API client
- **Bull**: Redis-based queue system
- **Mongoose**: MongoDB ODM
- **Axios**: HTTP client for game API
- **Swagger**: API documentation