import { Process, Processor } from '@nestjs/bull';
import { jobs, queues } from '../../../constant/mq.constant';
import { Job } from 'bull';

@Processor(queues.MESSAGE_SYNC_QUEUE)
export class GameToTelegramHandle {
  @Process(jobs.GAME_TO_TELEGRAM)
  async handleTask(job: Job) {
    const { message, targetMapping } = job.data;
    console.log(`Game→TG: ${message?.messageId} → ${targetMapping?.telegramGroupId || 'default'}`);
    
    const { Telegraf } = require('telegraf');
    const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);
    
    const options: any = { parse_mode: 'HTML' };
    if (targetMapping.telegramThreadId) {
      options.message_thread_id = targetMapping.telegramThreadId;
    }
    if (message.replyTo) {
      options.reply_to_message_id = message.replyTo;
    }

    await bot.telegram.sendMessage(
      targetMapping.telegramGroupId,
      `[${message.fromUser}] ${message.content}`,
      options
    );
  }
}
