================================================================
Files
================================================================

================
File: src/routes/telegram/telegram.controller.ts
================
  1: import { Controller, Post, Body, Get, Logger, HttpException, HttpStatus, Query } from '@nestjs/common';
  2: import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiQuery } from '@nestjs/swagger';
  3: import { SyncDto } from '../../dto/sync.dto';
  4: 
  5: @ApiTags('Telegram')
  6: @Controller('telegram')
  7: export class TelegramController {
  8:   private readonly logger = new Logger(TelegramController.name);
  9: 
 10:   /**
 11:    * Telegram消息同步到游戏
 12:    */
 13:   @Post('sync-to-game')
 14:   @ApiOperation({ summary: 'Telegram消息同步到游戏' })
 15:   @ApiBody({ type: SyncDto })
 16:   @ApiResponse({ status: 200, description: '同步成功' })
 17:   @ApiResponse({ status: 400, description: '请求参数错误' })
 18:   @ApiResponse({ status: 500, description: '服务器内部错误' })
 19:   async syncToGame(@Body() syncDto: SyncDto) {
 20:     try {
 21:       this.logger.log(`接收到Telegram消息同步请求: ${syncDto.messageId}`);
 22:       
 23:       // 验证必要参数
 24:       if (!syncDto.content || !syncDto.fromUser || !syncDto.messageId) {
 25:         throw new HttpException('缺少必要参数', HttpStatus.BAD_REQUEST);
 26:       }
 27: 
 28:       return {
 29:         success: true,
 30:         data: {
 31:           messageId: syncDto.messageId,
 32:           status: 'queued',
 33:           message: '消息已加入处理队列',
 34:         }
 35:       };
 36:     } catch (error) {
 37:       this.logger.error('Telegram消息同步失败:', error);
 38:       if (error instanceof HttpException) {
 39:         throw error;
 40:       }
 41:       throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
 42:     }
 43:   }
 44: 
 45:   /**
 46:    * 发送消息到Telegram群组
 47:    */
 48:   @Post('send-message')
 49:   @ApiOperation({ summary: '发送消息到Telegram群组' })
 50:   @ApiBody({
 51:     schema: {
 52:       type: 'object',
 53:       properties: {
 54:         chatId: { type: 'string', description: 'Telegram群组ID' },
 55:         message: { type: 'string', description: '消息内容' },
 56:         threadId: { type: 'string', description: '线程ID（可选）' },
 57:         fromUser: { type: 'string', description: '发送用户（可选）' },
 58:       },
 59:       required: ['chatId', 'message'],
 60:     },
 61:   })
 62:   @ApiResponse({ status: 200, description: '发送成功' })
 63:   async sendMessage(@Body() body: { 
 64:     chatId: string; 
 65:     message: string; 
 66:     threadId?: string; 
 67:     fromUser?: string;
 68:   }) {
 69:     throw new HttpException('功能已移除，请使用同步接口', HttpStatus.NOT_IMPLEMENTED);
 70:   }
 71: 
 72:   /**
 73:    * 获取群组信息
 74:    */
 75:   @Get('chat-info')
 76:   @ApiOperation({ summary: '获取Telegram群组信息' })
 77:   @ApiQuery({ name: 'chatId', description: 'Telegram群组ID' })
 78:   @ApiResponse({ status: 200, description: '获取成功' })
 79:   async getChatInfo(@Query('chatId') chatId: string) {
 80:     throw new HttpException('功能已移除', HttpStatus.NOT_IMPLEMENTED);
 81:   }
 82: 
 83:   /**
 84:    * Telegram服务健康检查
 85:    */
 86:   @Get('health')
 87:   @ApiOperation({ summary: 'Telegram服务健康检查' })
 88:   @ApiResponse({ status: 200, description: '服务正常' })
 89:   async healthCheck() {
 90:     return {
 91:       success: true,
 92:       data: {
 93:         status: 'healthy',
 94:         timestamp: new Date().toISOString(),
 95:       }
 96:     };
 97:   }
 98: 
 99:   /**
100:    * 发送文件到Telegram
101:    */
102:   @Post('send-document')
103:   @ApiOperation({ summary: '发送文件到Telegram群组' })
104:   @ApiBody({
105:     schema: {
106:       type: 'object',
107:       properties: {
108:         chatId: { type: 'string', description: 'Telegram群组ID' },
109:         document: { type: 'string', description: '文件内容或URL' },
110:         caption: { type: 'string', description: '文件说明（可选）' },
111:         threadId: { type: 'string', description: '线程ID（可选）' },
112:       },
113:       required: ['chatId', 'document'],
114:     },
115:   })
116:   @ApiResponse({ status: 200, description: '发送成功' })
117:   async sendDocument(@Body() body: { 
118:     chatId: string; 
119:     document: any; 
120:     caption?: string; 
121:     threadId?: string;
122:   }) {
123:     throw new HttpException('功能已移除，请使用同步接口', HttpStatus.NOT_IMPLEMENTED);
124:   }
125: }




================================================================
End of Codebase
================================================================
