import { Controller, Post, Body, Get, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { SyncDto } from '../../dto/sync.dto';

@ApiTags('游戏聊天')
@Controller('game')
export class GameController {
  private readonly logger = new Logger(GameController.name);

  /**
   * 游戏消息同步到Telegram
   */
  @Post('sync-to-telegram')
  @ApiOperation({ summary: '游戏消息同步到Telegram' })
  @ApiBody({ type: SyncDto })
  @ApiResponse({ status: 200, description: '同步成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async syncToTelegram(@Body() syncDto: SyncDto) {
    try {
      this.logger.log(`接收到游戏消息同步请求: ${syncDto.messageId}`);
      
      // 验证必要参数
      if (!syncDto.content || !syncDto.fromUser || !syncDto.messageId) {
        throw new HttpException('缺少必要参数', HttpStatus.BAD_REQUEST);
      }

      return {
        success: true,
        data: {
          messageId: syncDto.messageId,
          status: 'queued',
          message: '消息已加入处理队列',
        }
      };
    } catch (error) {
      this.logger.error('游戏消息同步失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('内部服务器错误', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 发送消息到游戏聊天
   */
  @Post('send-message')
  @ApiOperation({ summary: '发送消息到游戏聊天' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        channelId: { type: 'number', description: '游戏频道ID' },
        message: { type: 'string', description: '消息内容' },
        fromUser: { type: 'string', description: '发送用户' },
      },
      required: ['channelId', 'message'],
    },
  })
  @ApiResponse({ status: 200, description: '发送成功' })
  async sendMessage(@Body() body: { channelId: number; message: string; fromUser?: string }) {
    throw new HttpException('功能已移除，请使用同步接口', HttpStatus.NOT_IMPLEMENTED);
  }

  /**
   * 获取游戏聊天历史
   */
  @Get('chat-history')
  @ApiOperation({ summary: '获取游戏聊天历史' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getChatHistory(@Body() body: { channelId: number; limit?: number }) {
    throw new HttpException('功能已移除', HttpStatus.NOT_IMPLEMENTED);
  }

  /**
   * 游戏服务健康检查
   */
  @Get('health')
  @ApiOperation({ summary: '游戏服务健康检查' })
  @ApiResponse({ status: 200, description: '服务正常' })
  async healthCheck() {
    return {
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
      }
    };
  }

  /**
   * 获取频道信息
   */
  @Get('channel-info')
  @ApiOperation({ summary: '获取游戏频道信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getChannelInfo(@Body() body: { channelId: number }) {
    throw new HttpException('功能已移除', HttpStatus.NOT_IMPLEMENTED);
  }
}
