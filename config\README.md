# 配置文件说明

## 概述

本目录包含项目的统一配置文件，用于管理同步配置和系统设置。

## 配置文件

### 文件位置

- **默认配置文件**: `config/ad-filter-default-config.json` (系统默认配置，请勿修改)
- **用户配置文件**: `config/ad-filter-config.json` (用户自定义配置)
- **示例配置文件**: `config/ad-filter-config.example.json` (配置示例)

### 配置优先级

1. **用户自定义配置** (`ad-filter-config.json`) - 最高优先级
2. **默认配置文件** (`ad-filter-default-config.json`) - 系统默认
3. **内置备用配置** - 当文件加载失败时使用

### 配置结构

```json
{
  "keywords": ["关键词1", "关键词2"],
  "whitelist": ["白名单词1", "白名单词2"],
  "urlPatterns": ["t\\.me", "telegram\\.me"],
  "contactPatterns": ["微信", "QQ", "电话"],
  "thresholds": {
    "repeatedCharThreshold": 5,
    "capsRatio": 0.5,
    "minCapsLength": 10
  },
  "enabled": true,
  "_metadata": {
    "version": "1.0.0",
    "lastUpdated": "2024-01-01T00:00:00.000Z",
    "description": "配置文件描述"
  }
}
```

### 配置项说明

| 配置项                             | 类型     | 说明                             |
| ---------------------------------- | -------- | -------------------------------- |
| `keywords`                         | string[] | 广告关键词黑名单                 |
| `whitelist`                        | string[] | 白名单关键词（优先级高于黑名单） |
| `urlPatterns`                      | string[] | URL 正则表达式模式               |
| `contactPatterns`                  | string[] | 联系方式关键词                   |
| `thresholds.repeatedCharThreshold` | number   | 重复字符阈值                     |
| `thresholds.capsRatio`             | number   | 大写字母比例阈值                 |
| `thresholds.minCapsLength`         | number   | 大写检测最小长度                 |
| `enabled`                          | boolean  | 是否启用广告过滤                 |
| `_metadata`                        | object   | 元数据信息（自动生成）           |

## API 接口

### 添加黑名单关键词

```http
POST /admin/ad-keywords
Content-Type: application/json

{
  "keywords": ["新关键词1", "新关键词2"]
}
```

### 移除黑名单关键词

```http
DELETE /admin/ad-keywords
Content-Type: application/json

{
  "keywords": ["要删除的关键词"]
}
```

### 添加白名单关键词

```http
POST /admin/whitelist-keywords
Content-Type: application/json

{
  "keywords": ["白名单关键词"]
}
```

### 更新配置

```http
PUT /admin/ad-filter-config
Content-Type: application/json

{
  "enabled": true,
  "thresholds": {
    "repeatedCharThreshold": 6
  }
}
```

### 切换过滤开关

```http
POST /admin/toggle-ad-filter
Content-Type: application/json

{
  "enabled": false
}
```

## 特性

### 🔄 自动持久化

- 通过 API 修改的配置会自动保存到本地文件
- 项目重启后配置自动加载
- 支持增量保存，只保存与默认配置不同的部分

### 📊 配置优先级

1. **自定义配置**（代码中传入的配置）
2. **本地文件配置**（`ad-filter-config.json`）
3. **默认配置**（代码中的默认值）

### 🛡️ 向后兼容

- 保持现有 API 接口不变
- 支持渐进式迁移到数据库
- 配置文件格式向前兼容

### 🔍 智能去重

- 添加关键词时自动去重
- 只有实际发生变更时才保存文件
- 优化性能，减少不必要的 I/O 操作

## 日志输出

系统会输出以下日志信息：

- ✅ 成功加载广告过滤配置文件
- 📝 广告过滤配置文件不存在，将使用默认配置
- ❌ 加载/保存配置文件失败
- ✅ 广告过滤配置已保存到文件

## 注意事项

1. **配置文件管理**:

   - `ad-filter-default-config.json`: 系统默认配置，**请勿手动修改**
   - `ad-filter-config.json`: 用户自定义配置，可以手动编辑或通过API修改
   - 系统会自动从默认配置文件加载基础配置，然后应用用户自定义配置

2. **文件权限**: 确保应用有读写配置文件的权限

3. **备份**: 建议定期备份用户配置文件 `ad-filter-config.json`

4. **格式**: 配置文件必须是有效的 JSON 格式

5. **编码**: 文件编码必须是 UTF-8

6. **正则表达式**: 在JSON文件中，正则表达式以字符串形式存储，系统会自动转换为RegExp对象

7. **迁移**: 如果需要更强大的配置管理，可以考虑迁移到数据库存储

## 迁移到数据库

当需要迁移到数据库时，可以：

1. 保持现有 API 接口不变
2. 修改 `AdFilterConfigManager` 的存储后端
3. 提供数据迁移工具，将文件配置导入数据库
4. 支持配置来源切换（文件 ↔ 数据库）
