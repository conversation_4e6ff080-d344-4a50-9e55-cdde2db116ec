import { Process, Processor } from '@nestjs/bull';
import { jobs, queues } from '../../../constant/mq.constant';
import { Job } from 'bull';

@Processor(queues.MESSAGE_SYNC_QUEUE)
export class TelegramToGameHandle {
  @Process(jobs.TELEGRAM_TO_GAME)
  async handleTask(job: Job) {
    const { message, config } = job.data;
    console.log(`TG→Game: ${message?.messageId} → ${config?.gameChatChannelId}`);
    
    const axios = require('axios');
    await axios.post(
      `${process.env.GAME_CHAT_API_URL}/api/chat/message`,
      {
        chatId: config.gameChatChannelId,
        content: message.content,
        playerId: message.fromUser,
        isAdmin: message.isAdmin || false,
        replyTo: message.replyTo,
      },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000,
      }
    );
  }
}
